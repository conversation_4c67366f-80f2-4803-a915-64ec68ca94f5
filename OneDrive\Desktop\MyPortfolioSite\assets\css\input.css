@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables */
:root {
    --primary: #1e3a8a;
    --secondary: #f59e0b;
    --dark: #0f172a;
    --light: #f8fafc;
}

/* Base Styles */
body {
    font-family: 'Poppins', sans-serif;
    background-color: #f8fafc;
    color: var(--dark);
    overflow-x: hidden;
}

/* Component Styles */
.section-title {
    position: relative;
    display: inline-block;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 60px;
    height: 4px;
    background-color: var(--secondary);
    border-radius: 2px;
}

.skill-item:hover .skill-bar {
    width: 100% !important;
}

.project-card {
    transition: all 0.3s ease;
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.testimonial-card {
    transition: all 0.3s ease;
}

.testimonial-card:hover {
    transform: scale(1.02);
}

@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
    100% { transform: translateY(0px); }
}

.floating {
    animation: float 6s ease-in-out infinite;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #1e40af;
}
