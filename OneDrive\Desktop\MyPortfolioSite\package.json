{"name": "myportfoliosite", "version": "1.0.0", "description": "<PERSON>'s Portfolio Website", "main": "index.js", "scripts": {"build-css": "tailwindcss -i ./assets/css/input.css -o ./assets/css/style.css --watch", "build-css-prod": "tailwindcss -i ./assets/css/input.css -o ./assets/css/style.css --minify", "dev": "npm run build-css", "build": "npm run build-css-prod", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["portfolio", "web-development", "tailwindcss"], "author": "<PERSON>", "license": "ISC", "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.5", "tailwindcss": "^4.1.10"}}